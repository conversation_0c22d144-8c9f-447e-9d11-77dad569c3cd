/* CARD STYLING */
.stack__card {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 12;

  width: 100%;
  display: flex;
  justify-content: space-around;
  gap: 2rem;
  flex-wrap: wrap;
}

.card__container {
  padding: 12px;
  width: 10rem;
  height: 11rem;
  background: rgba(7, 7, 7, 0.771);
  backdrop-filter: blur(10px);
  border-radius: 7px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  text-align: center;
  color: #fff;
  border: 1px solid rgba(255, 255, 255, 0.1);

  transform: translateY(0);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card__container:hover {
  transform: translateY(-8px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
  border-color: rgba(84, 98, 255, 0.3);
}

.icon__container .card__icon {
  width: 80px;
  height: auto;
}

.card__progress-container {
  width: 100%;
  height: 10px;
  border-radius: 5px;
  margin-top: 15px;
  overflow: hidden;

  transform: translateY(10px);
  opacity: 0;

  border: 1px solid var(--color-primary);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.card__container:hover .card__progress-container {
  transform: translateY(0px);
  opacity: 1;
}

.card__progressbar {
  height: 100%;
  background-color: var(--color-primary);
  font-size: 0.6rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}
