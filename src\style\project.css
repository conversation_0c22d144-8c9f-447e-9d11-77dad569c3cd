/* project - 2 style */
.project__section .container {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  grid-gap: 10px;
  width: 100%;
  margin-top: 3rem;
}

.project__section h2 {
  font-size: 45px;
  font-weight: 300;
  margin: 10px;
  border: 1px solid red;
  color: var(--color-secondary-dark);
}

.project__section h2 span {
  font-size: 30px;
  color: var(--color-secondary-dark);
}

.project__section p {
  font-size: 16px;
}

.project__section .container > div {
  position: relative;
  cursor: pointer;
  height: 200px;
  background-size: cover;
  background-attachment: fixed;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  transition: all 0.5s ease-in;
}

.project__section .container > div:hover {
  transform: scale(0.98);
  opacity: 0.7;
}

.overlay {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: var(--overlay-bg);
  color: var(--overlay-text);
  height: 0;
  overflow: hidden;
  transition: height 0.3s ease-in-out;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

span.overlay-stack {
  margin: 5px;
  font-size: 10px;
  border: 1px solid var(--color-primary);
  padding: 2px 5px;
  border-radius: 5px;
}

.overlay-description {
  font-size: 12px;
  margin: 5px 0 0;
  text-align: center;
  border-radius: 5px;
}

.project__section .container > div:hover .overlay {
  height: 100%;
}

.project__section .container > div:nth-of-type(1) {
  grid-column: 1/3;
}

.project__section .container > div:nth-child(6) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(9) {
  grid-column: 3/5;
}

.project__section .container > div:nth-child(10) {
  grid-column: 1/3;
}

.bg1 {
  background: var(--bg1);
}

.bg2 {
  background: var(--bg2);
  color: var(--text-color);
  font-weight: 300;
}

.project-overlay {
  position: absolute;
  bottom: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0, 0, 0, 0.5);
  padding: 10px;
  border-radius: 5px;
}

.project-overlay h3 {
  margin: 0;
  font-size: 20px;
}

.project-overlay p {
  margin: 5px 0 0;
  font-size: 14px;
}

/* Modal Styles */
.modal-background {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.8);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  z-index: 1000;
  animation: fadeIn 0.3s;
}

.modal-content {
  background: #fff;
  padding: 20px;
  border-radius: 10px;
  max-width: 80%;
  max-height: 80%;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.modal__container-image {
  width: 100%;
  height: auto;
  margin-bottom: 20px;
}

.modal__image {
  width: 400px;
  height: auto;
}

.modal-info {
  text-align: center;
}

.modal-info h2 {
  margin: 0 10px;
  font-size: 18px;
  color: #333;
}

.modal-info p {
  font-size: 16px;
  color: #666;
  width: 60%;
  margin: auto;
  text-align: justify;
  word-spacing: 1px;
}

.modal__stacks .stack {
  font-size: 12px;
  color: var(--color-light);
  padding: 7px;
  border-radius: 5px;
  margin: 20px 5px;
  display: inline-block;
  background: var(--color-primary);
}

.modal__git-link {
  padding: 0.5rem 1.5rem;
  color: #fff;
  text-decoration: none;
  border-radius: 0.5rem;

  background-color: var(--color-secondary-dark);
  display: inline-flex;
  align-items: center;
  transition: var(--transition);
}

.modal__git-link:hover {
  background-color: var(--color-primary); /* Change to your primary color */
}

.modal__git-link i {
  margin-right: 5px; /* Space between icon and text */
}

.project__section .container .project-card {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.project__section .container .project-card:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.modal-background {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.modal-background.open {
  opacity: 1;
}

/* close modal button */

.modal-close {
  /* Positioning */
  position: absolute;
  top: 20px;
  right: 20px;
  z-index: 1000;

  /* Dimensions and Layout */
  width: 30px;
  height: 30px;
  display: flex;
  justify-content: center;
  align-items: center;

  /* Visual Design */
  background-color: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  backdrop-filter: blur(5px);
  color: var(--color-light);
  font-size: 20px;
  cursor: pointer;
  transition: var(--transition);
}

.modal-close:hover {
  background-color: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.modal-close:active {
  transform: scale(0.95);
}
