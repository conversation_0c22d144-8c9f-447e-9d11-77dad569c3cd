import react from "../images/icons/react.svg";
import git from "../images/icons/git.svg";
import html from "../images/icons/html.svg";
import css from "../images/icons/css.svg";
import js from "../images/icons/js.svg";
import vuejs from "../images/icons/vuejs.svg";

export const CLASSES = {
  HERO: "hero",
  HERO_CONTENT: "hero__content hero-flex",
  HERO_CONTENT_CONTAINER: "hero__content-container",
  MAIN_TITLE: "main__title",
  SUB_TITLE: "sub__title",
  BTN_HERO: "btn btn--hero",
  HERO_IMAGE: "hero__img about__hero-content",
  STACK_CARD: "stack__card",
  CARD_CONTAINER: "card__container",
  ICON_CONTAINER: "icon__container",
  CARD_ICON: "card__icon",
  CARD_TITLE: "card__title",
  PROGRESS_CONTAINER: "card__progress-container",
  PROGRESSBAR: "card__progressbar",
  TEXT_BOX: "text__box",
};

export const TEXTS = {
  BUTTON_TEXT: "Get Resume",
  TITLES: ["Turning Designs", "into Reality"],
  ABOUT_TEXT:
    "I am a passionate frontend developer with a strong focus on creating visually appealing and user-friendly web applications. With a solid foundation in HTML, CSS, and JavaScript, I bring designs to life and ensure seamless user experiences. I am constantly expanding my skill set and staying up-to-date with the latest frontend technologies to deliver cutting-edge solutions. I am dedicated to delivering high-quality code and collaborating with cross-functional teams to create exceptional digital experiences. Let's work together to bring your ideas to life!",
};

export const STACK_DATA = [
  { icon: vuejs, text: "Vuejs", width: 60 },
  { icon: git, text: "Git", width: 60 },
  { icon: html, text: "HTML", width: 80 },
  { icon: css, text: "CSS", width: 80 },
  { icon: js, text: "JavaScript", width: 70 },
  { icon: react, text: "React", width: 70 },
];
