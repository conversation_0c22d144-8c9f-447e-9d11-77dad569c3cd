.parent__container {
  z-index: 89;
  position: absolute;
  width: 80%;
  height: 80%;
  /* background: linear-gradient(270deg, #ff007b, #007bff, #00ff7b); */
  /* background-size: 600% 600%; */
  animation: backgroundAnimation 10s ease infinite;
  overflow: hidden;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s;
}

.parent__container:hover canvas {
  transform: scale(1.05);
  cursor: pointer;
}

canvas {
  /* width: 100% !important;
  height: 100% !important;
  object-fit: cover;
  border-radius: 15px; */
  transition: transform 0.3s;
}

.tooltip {
  position: absolute;
  padding: 5px 10px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 5px;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s;
}
.theme-selector {
  position: absolute;
  top: 30px;
  right: 10px;
  display: flex;
  gap: 10px;
}

.theme-button {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  cursor: pointer;
  border: 2px solid #fff;
}
.details {
  position: absolute;
  bottom: 10px;
  left: 50%;
  transform: translateX(-50%);
  padding: 10px 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: #fff;
  border-radius: 5px;
  opacity: 0;
  transition: opacity 0.3s;
}
